using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

namespace DinPresto.ViewModels
{
    public abstract partial class BaseViewModel : ObservableObject
    {
        [ObservableProperty]
        private bool isBusy;

        [ObservableProperty]
        private string title = string.Empty;

        [ObservableProperty]
        private string busyText = "Cargando...";

        [ObservableProperty]
        private bool hasErrors;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        public ObservableCollection<string> Errors { get; } = new();

        protected virtual void OnBusyChanged()
        {
            // Override en clases derivadas si es necesario
        }

        protected void SetBusy(bool busy, string? busyText = null)
        {
            IsBusy = busy;
            if (!string.IsNullOrEmpty(busyText))
            {
                BusyText = busyText;
            }
        }

        protected void AddError(string error)
        {
            Errors.Add(error);
            HasErrors = Errors.Count > 0;
            ErrorMessage = string.Join(Environment.NewLine, Errors);
        }

        protected void ClearErrors()
        {
            Errors.Clear();
            HasErrors = false;
            ErrorMessage = string.Empty;
        }

        [RelayCommand]
        protected virtual void ClearError()
        {
            ClearErrors();
        }

        protected async Task ExecuteAsync(Func<Task> operation, string? busyText = null)
        {
            if (IsBusy) return;

            try
            {
                SetBusy(true, busyText);
                ClearErrors();
                await operation();
            }
            catch (Exception ex)
            {
                AddError($"Error: {ex.Message}");
            }
            finally
            {
                SetBusy(false);
            }
        }

        protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string? busyText = null)
        {
            if (IsBusy) return default;

            try
            {
                SetBusy(true, busyText);
                ClearErrors();
                return await operation();
            }
            catch (Exception ex)
            {
                AddError($"Error: {ex.Message}");
                return default;
            }
            finally
            {
                SetBusy(false);
            }
        }
    }
}
