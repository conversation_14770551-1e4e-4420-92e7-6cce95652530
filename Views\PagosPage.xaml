<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.PagosPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="💳 Gestión de Pagos" FontSize="28" FontWeight="Bold" VerticalAlignment="Center"/>
            <Button Grid.Column="1" Content="➕ Registrar Pago" Background="{ThemeResource SystemAccentColor}" 
                   Foreground="White" Padding="15,10" CornerRadius="5"/>
        </Grid>

        <!-- Search and Filters -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20" BorderBrush="LightGray" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" PlaceholderText="🔍 Buscar por cliente o número de recibo..." 
                        Margin="0,0,10,0" VerticalAlignment="Center"/>
                <DatePicker Grid.Column="1" Header="Desde" Margin="0,0,10,0" Width="120"/>
                <DatePicker Grid.Column="2" Header="Hasta" Margin="0,0,10,0" Width="120"/>
                <Button Grid.Column="3" Content="🔍 Buscar" Padding="15,8"/>
            </Grid>
        </Border>

        <!-- Payments List -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" BorderBrush="LightGray" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <Border Grid.Row="0" Background="LightGray" Padding="15,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Recibo" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="Cliente" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="Préstamo" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="Fecha Pago" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="Monto" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="Método" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="6" Text="Acciones" FontWeight="SemiBold"/>
                    </Grid>
                </Border>

                <!-- List Content -->
                <ListView Grid.Row="1" Padding="0">
                    <!-- Sample Data -->
                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="#001234" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Juan Pérez García" FontWeight="SemiBold"/>
                                <TextBlock Text="001-1234567-8" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="$50,000.00" FontWeight="SemiBold"/>
                                <TextBlock Text="Cuota #5" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="3" Text="15/12/2024" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$2,850.00" VerticalAlignment="Center" FontWeight="SemiBold" Foreground="Green"/>
                            <Border Grid.Column="5" Background="Blue" CornerRadius="10" Padding="8,4" HorizontalAlignment="Center">
                                <TextBlock Text="Efectivo" Foreground="White" FontSize="12"/>
                            </Border>
                            
                            <StackPanel Grid.Column="6" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver recibo" Padding="8"/>
                                <Button Content="🖨️" ToolTipService.ToolTip="Imprimir" Padding="8"/>
                                <Button Content="📧" ToolTipService.ToolTip="Enviar por email" Padding="8"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>

                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="#001235" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="María González López" FontWeight="SemiBold"/>
                                <TextBlock Text="001-9876543-2" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="$25,000.00" FontWeight="SemiBold"/>
                                <TextBlock Text="Cuota #8" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="3" Text="14/12/2024" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$2,450.00" VerticalAlignment="Center" FontWeight="SemiBold" Foreground="Green"/>
                            <Border Grid.Column="5" Background="Purple" CornerRadius="10" Padding="8,4" HorizontalAlignment="Center">
                                <TextBlock Text="Transferencia" Foreground="White" FontSize="12"/>
                            </Border>
                            
                            <StackPanel Grid.Column="6" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver recibo" Padding="8"/>
                                <Button Content="🖨️" ToolTipService.ToolTip="Imprimir" Padding="8"/>
                                <Button Content="📧" ToolTipService.ToolTip="Enviar por email" Padding="8"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>

                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="#001236" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Carlos Rodríguez Martínez" FontWeight="SemiBold"/>
                                <TextBlock Text="001-5555555-5" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="$75,000.00" FontWeight="SemiBold"/>
                                <TextBlock Text="Pago Total" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="3" Text="13/12/2024" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$45,000.00" VerticalAlignment="Center" FontWeight="SemiBold" Foreground="Green"/>
                            <Border Grid.Column="5" Background="Green" CornerRadius="10" Padding="8,4" HorizontalAlignment="Center">
                                <TextBlock Text="Cheque" Foreground="White" FontSize="12"/>
                            </Border>
                            
                            <StackPanel Grid.Column="6" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver recibo" Padding="8"/>
                                <Button Content="🖨️" ToolTipService.ToolTip="Imprimir" Padding="8"/>
                                <Button Content="📧" ToolTipService.ToolTip="Enviar por email" Padding="8"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>
                </ListView>
            </Grid>
        </Border>
    </Grid>
</Page>
