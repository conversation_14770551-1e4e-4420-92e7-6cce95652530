using Microsoft.EntityFrameworkCore;
using DinPresto.Models;

namespace DinPresto.Data
{
    public class DinPrestoContext : DbContext
    {
        public DinPrestoContext(DbContextOptions<DinPrestoContext> options) : base(options)
        {
        }

        public DbSet<Cliente> Clientes { get; set; }
        public DbSet<Prestamo> Prestamos { get; set; }
        public DbSet<Cuota> Cuotas { get; set; }
        public DbSet<Pago> Pagos { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configuración de Cliente
            modelBuilder.Entity<Cliente>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Nombre).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Apellidos).IsRequired().HasMaxLength(100);
                entity.Property(e => e.DocumentoIdentidad).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Telefono).HasMaxLength(20);
                entity.Property(e => e.Direccion).HasMaxLength(200);
                entity.Property(e => e.FechaRegistro).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.Activo).HasDefaultValue(true);

                // Índices
                entity.HasIndex(e => e.DocumentoIdentidad).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Configuración de Préstamo
            modelBuilder.Entity<Prestamo>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.MontoCapital).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.TasaInteresAnual).HasColumnType("decimal(5,4)").IsRequired();
                entity.Property(e => e.MontoTotal).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalIntereses).HasColumnType("decimal(18,2)");
                entity.Property(e => e.SaldoPendiente).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MontoPagado).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Observaciones).HasMaxLength(500);
                entity.Property(e => e.FechaCreacion).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // Relaciones
                entity.HasOne(e => e.Cliente)
                      .WithMany(c => c.Prestamos)
                      .HasForeignKey(e => e.ClienteId)
                      .OnDelete(DeleteBehavior.Restrict);

                // Índices
                entity.HasIndex(e => e.ClienteId);
                entity.HasIndex(e => e.Estado);
                entity.HasIndex(e => e.FechaInicio);
            });

            // Configuración de Cuota
            modelBuilder.Entity<Cuota>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.MontoCapital).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.MontoInteres).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.MontoCuota).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.SaldoCapital).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MontoPagado).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MontoMora).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Observaciones).HasMaxLength(200);

                // Relaciones
                entity.HasOne(e => e.Prestamo)
                      .WithMany(p => p.Cuotas)
                      .HasForeignKey(e => e.PrestamoId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Índices
                entity.HasIndex(e => e.PrestamoId);
                entity.HasIndex(e => e.FechaVencimiento);
                entity.HasIndex(e => e.Estado);
                entity.HasIndex(e => new { e.PrestamoId, e.NumeroCuota }).IsUnique();
            });

            // Configuración de Pago
            modelBuilder.Entity<Pago>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.MontoCapital).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.MontoInteres).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.MontoMora).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MontoTotal).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.NumeroReferencia).HasMaxLength(100);
                entity.Property(e => e.Observaciones).HasMaxLength(200);
                entity.Property(e => e.UsuarioRegistro).HasMaxLength(50).IsRequired();
                entity.Property(e => e.MotivoAnulacion).HasMaxLength(200);
                entity.Property(e => e.FechaRegistro).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // Relaciones
                entity.HasOne(e => e.Prestamo)
                      .WithMany(p => p.Pagos)
                      .HasForeignKey(e => e.PrestamoId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Cuota)
                      .WithMany(c => c.Pagos)
                      .HasForeignKey(e => e.CuotaId)
                      .OnDelete(DeleteBehavior.SetNull);

                // Índices
                entity.HasIndex(e => e.PrestamoId);
                entity.HasIndex(e => e.CuotaId);
                entity.HasIndex(e => e.FechaPago);
                entity.HasIndex(e => e.Anulado);
            });
        }
    }
}
