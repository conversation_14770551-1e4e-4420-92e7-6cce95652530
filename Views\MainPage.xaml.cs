using Microsoft.UI.Xaml.Controls;
using DinPresto.ViewModels;
using DinPresto.Services;

namespace DinPresto.Views
{
    /// <summary>
    /// Página principal de la aplicación DinPresto
    /// </summary>
    public sealed partial class MainPage : Page
    {
        public MainViewModel ViewModel { get; private set; }
        private INavigationService _navigationService;

        public MainPage()
        {
            this.InitializeComponent();
        }

        public MainPage(MainViewModel viewModel, INavigationService navigationService)
        {
            this.InitializeComponent();
            ViewModel = viewModel;
            _navigationService = navigationService;

            // Configurar el servicio de navegación con el Frame
            navigationService.Frame = ContentFrame;

            // Navegar al Dashboard por defecto
            navigationService.NavigateTo<DashboardPage>(viewModel);
        }

        private void DashboardButton_Click(object sender, Microsoft.UI.Xaml.RoutedEventArgs e)
        {
            _navigationService?.NavigateTo<DashboardPage>(ViewModel);
        }

        private void ClientesButton_Click(object sender, Microsoft.UI.Xaml.RoutedEventArgs e)
        {
            _navigationService?.NavigateTo<ClientesPage>();
        }

        private void PrestamosButton_Click(object sender, Microsoft.UI.Xaml.RoutedEventArgs e)
        {
            _navigationService?.NavigateTo<PrestamosPage>();
        }

        private void PagosButton_Click(object sender, Microsoft.UI.Xaml.RoutedEventArgs e)
        {
            _navigationService?.NavigateTo<PagosPage>();
        }

        private void ReportesButton_Click(object sender, Microsoft.UI.Xaml.RoutedEventArgs e)
        {
            _navigationService?.NavigateTo<ReportesPage>();
        }

        private void ConfiguracionButton_Click(object sender, Microsoft.UI.Xaml.RoutedEventArgs e)
        {
            _navigationService?.NavigateTo<ConfiguracionPage>();
        }
    }
}
