using Microsoft.UI.Xaml.Controls;
using DinPresto.ViewModels;

namespace DinPresto.Views
{
    public sealed partial class DashboardPage : Page
    {
        public MainViewModel ViewModel { get; }

        public DashboardPage()
        {
            this.InitializeComponent();
            // El ViewModel se inyectará a través de DI
        }

        public DashboardPage(MainViewModel viewModel)
        {
            this.InitializeComponent();
            ViewModel = viewModel;
        }
    }
}
