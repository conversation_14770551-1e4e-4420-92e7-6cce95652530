﻿<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.ConfiguracionPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ScrollViewer Padding="20">
        <StackPanel Spacing="20">
            <!-- Header -->
            <TextBlock Text="⚙️ Configuración del Sistema" FontSize="28" FontWeight="Bold"/>

            <!-- General Settings -->
            <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="LightGray" BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock Text="🏢 Configuración General" FontSize="18" FontWeight="SemiBold"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Column="0" Grid.Row="0" Spacing="5" Margin="0,0,10,15">
                            <TextBlock Text="Nombre de la Empresa:" FontWeight="SemiBold"/>
                            <TextBox Text="DinPresto Financial Services" />
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="0" Spacing="5" Margin="10,0,0,15">
                            <TextBlock Text="RNC/Registro:" FontWeight="SemiBold"/>
                            <TextBox Text="130-12345-6" />
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="1" Spacing="5" Margin="0,0,10,15">
                            <TextBlock Text="Dirección:" FontWeight="SemiBold"/>
                            <TextBox Text="Av. Principal #123, Santo Domingo" />
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="1" Spacing="5" Margin="10,0,0,15">
                            <TextBlock Text="Teléfono:" FontWeight="SemiBold"/>
                            <TextBox Text="(*************" />
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="2" Spacing="5" Margin="0,0,10,15">
                            <TextBlock Text="Email:" FontWeight="SemiBold"/>
                            <TextBox Text="<EMAIL>" />
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="2" Spacing="5" Margin="10,0,0,15">
                            <TextBlock Text="Moneda:" FontWeight="SemiBold"/>
                            <ComboBox SelectedIndex="0" HorizontalAlignment="Stretch">
                                <ComboBoxItem Content="Peso Dominicano (DOP)"/>
                                <ComboBoxItem Content="Dólar Americano (USD)"/>
                                <ComboBoxItem Content="Euro (EUR)"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Interest Rates Settings -->
            <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="LightGray" BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock Text="💰 Configuración de Tasas de Interés" FontSize="18" FontWeight="SemiBold"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Column="0" Grid.Row="0" Spacing="5" Margin="0,0,10,15">
                            <TextBlock Text="Tasa Mínima Anual (%):" FontWeight="SemiBold"/>
                            <NumberBox Value="12" Minimum="0" Maximum="100" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="0" Spacing="5" Margin="10,0,10,15">
                            <TextBlock Text="Tasa Máxima Anual (%):" FontWeight="SemiBold"/>
                            <NumberBox Value="36" Minimum="0" Maximum="100" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Grid.Row="0" Spacing="5" Margin="10,0,0,15">
                            <TextBlock Text="Tasa por Defecto (%):" FontWeight="SemiBold"/>
                            <NumberBox Value="18" Minimum="0" Maximum="100" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="1" Spacing="5" Margin="0,0,10,15">
                            <TextBlock Text="Tasa de Mora (%):" FontWeight="SemiBold"/>
                            <NumberBox Value="5" Minimum="0" Maximum="50" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="1" Spacing="5" Margin="10,0,10,15">
                            <TextBlock Text="Días de Gracia:" FontWeight="SemiBold"/>
                            <NumberBox Value="5" Minimum="0" Maximum="30" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Loan Settings -->
            <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="LightGray" BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock Text="🏦 Configuración de Préstamos" FontSize="18" FontWeight="SemiBold"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Column="0" Grid.Row="0" Spacing="5" Margin="0,0,10,15">
                            <TextBlock Text="Monto Mínimo:" FontWeight="SemiBold"/>
                            <NumberBox Value="5000" Minimum="0" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="0" Spacing="5" Margin="10,0,10,15">
                            <TextBlock Text="Monto Máximo:" FontWeight="SemiBold"/>
                            <NumberBox Value="500000" Minimum="0" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Grid.Row="0" Spacing="5" Margin="10,0,0,15">
                            <TextBlock Text="Plazo Mínimo (meses):" FontWeight="SemiBold"/>
                            <NumberBox Value="6" Minimum="1" Maximum="600" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="1" Spacing="5" Margin="0,0,10,15">
                            <TextBlock Text="Plazo Máximo (meses):" FontWeight="SemiBold"/>
                            <NumberBox Value="60" Minimum="1" Maximum="600" SpinButtonPlacementMode="Inline"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="1" Spacing="5" Margin="10,0,10,15">
                            <TextBlock Text="Tipo de Amortización por Defecto:" FontWeight="SemiBold"/>
                            <ComboBox SelectedIndex="0" HorizontalAlignment="Stretch">
                                <ComboBoxItem Content="Francés (Cuotas Fijas)"/>
                                <ComboBoxItem Content="Alemán (Capital Fijo)"/>
                                <ComboBoxItem Content="Americano (Solo Intereses)"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- System Settings -->
            <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="LightGray" BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock Text="🔧 Configuración del Sistema" FontSize="18" FontWeight="SemiBold"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Column="0" Grid.Row="0" Spacing="5" Margin="0,0,10,15">
                            <TextBlock Text="Formato de Fecha:" FontWeight="SemiBold"/>
                            <ComboBox SelectedIndex="0" HorizontalAlignment="Stretch">
                                <ComboBoxItem Content="dd/MM/yyyy"/>
                                <ComboBoxItem Content="MM/dd/yyyy"/>
                                <ComboBoxItem Content="yyyy-MM-dd"/>
                            </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="0" Spacing="5" Margin="10,0,0,15">
                            <TextBlock Text="Idioma:" FontWeight="SemiBold"/>
                            <ComboBox SelectedIndex="0" HorizontalAlignment="Stretch">
                                <ComboBoxItem Content="Español"/>
                                <ComboBoxItem Content="English"/>
                            </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="1" Spacing="5" Margin="0,0,10,15">
                            <CheckBox Content="Enviar recordatorios automáticos" IsChecked="True"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="1" Spacing="5" Margin="10,0,0,15">
                            <CheckBox Content="Generar respaldos automáticos" IsChecked="True"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="2" Spacing="5" Margin="0,0,10,15">
                            <CheckBox Content="Notificaciones por email" IsChecked="False"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="2" Spacing="5" Margin="10,0,0,15">
                            <CheckBox Content="Modo oscuro" IsChecked="False"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Action Buttons -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <Button Grid.Column="1" Content="💾 Guardar Cambios" Background="{ThemeResource SystemAccentColor}" 
                       Foreground="White" Padding="20,10" Margin="0,0,10,0"/>
                <Button Grid.Column="2" Content="🔄 Restaurar Valores" Padding="20,10" Margin="0,0,10,0"/>
                <Button Grid.Column="3" Content="📤 Exportar Configuración" Padding="20,10"/>
            </Grid>
        </StackPanel>
    </ScrollViewer>
</Page>

