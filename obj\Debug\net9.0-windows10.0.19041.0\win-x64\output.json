{"GeneratedCodeFiles": [], "GeneratedXamlFiles": ["D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\App.xaml", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\ClientesPage.xaml", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\ConfiguracionPage.xaml", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\DashboardPage.xaml", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\MainPage.xaml", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\PagosPage.xaml", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\PrestamosPage.xaml", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\ReportesPage.xaml"], "GeneratedXbfFiles": ["D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\App.xbf", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\ClientesPage.xbf", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\ConfiguracionPage.xbf", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\DashboardPage.xbf", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\MainPage.xbf", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\PagosPage.xbf", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\PrestamosPage.xbf", "D:\\Software3\\Dinpresto\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\Views\\ReportesPage.xbf"], "GeneratedXamlPagesFiles": [], "MSBuildLogEntries": [{"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 2:45:32 a. m.: 981 perfXC_StartPass2, <PERSON><PERSON><PERSON><PERSON>"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 2:45:33 a. m.:   2 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 2:45:33 a. m.:  96 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 2:45:33 a. m.: 116 perfXC_RestoredGeneratedPass2CodeFileBackup"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 2:45:33 a. m.: 121 perfXC_RestoredTypeInfoBackup"}]}