using DinPresto.Models;

namespace DinPresto.Services
{
    public interface IAmortizacionService
    {
        /// <summary>
        /// Calcula la tabla de amortización para un préstamo
        /// </summary>
        /// <param name="prestamo">Datos del préstamo</param>
        /// <returns>Lista de cuotas calculadas</returns>
        List<Cuota> CalcularTablaAmortizacion(Prestamo prestamo);

        /// <summary>
        /// Calcula el monto de la cuota para un tipo de amortización específico
        /// </summary>
        /// <param name="capital">Monto del capital</param>
        /// <param name="tasaMensual">Tasa de interés mensual (decimal)</param>
        /// <param name="plazoMeses">Plazo en meses</param>
        /// <param name="tipoAmortizacion">Tipo de amortización</param>
        /// <param name="numeroCuota">Número de cuota (para cálculos específicos)</param>
        /// <returns>Monto de la cuota</returns>
        decimal CalcularMontoCuota(decimal capital, decimal tasaMensual, int plazoMeses, 
                                  TipoAmortizacion tipoAmortizacion, int numeroCuota = 1);

        /// <summary>
        /// Calcula el total de intereses para un préstamo
        /// </summary>
        /// <param name="prestamo">Datos del préstamo</param>
        /// <returns>Total de intereses</returns>
        decimal CalcularTotalIntereses(Prestamo prestamo);

        /// <summary>
        /// Valida si los parámetros del préstamo son válidos para el cálculo
        /// </summary>
        /// <param name="prestamo">Datos del préstamo</param>
        /// <returns>Resultado de la validación</returns>
        (bool esValido, string mensaje) ValidarParametrosPrestamo(Prestamo prestamo);
    }
}
