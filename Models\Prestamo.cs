using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DinPresto.Models
{
    public class Prestamo
    {
        public int Id { get; set; }

        [Required]
        public int ClienteId { get; set; }

        [Required(ErrorMessage = "El monto del préstamo es obligatorio")]
        [Range(1, double.MaxValue, ErrorMessage = "El monto debe ser mayor a 0")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoCapital { get; set; }

        [Required(ErrorMessage = "La tasa de interés es obligatoria")]
        [Range(0.01, 100, ErrorMessage = "La tasa debe estar entre 0.01% y 100%")]
        [Column(TypeName = "decimal(5,4)")]
        public decimal TasaInteresAnual { get; set; }

        [Required(ErrorMessage = "El plazo es obligatorio")]
        [Range(1, 600, ErrorMessage = "El plazo debe estar entre 1 y 600 meses")]
        public int PlazoMeses { get; set; }

        [Required]
        public TipoAmortizacion TipoAmortizacion { get; set; }

        [Required]
        public DateTime FechaInicio { get; set; }

        public DateTime FechaVencimiento { get; set; }

        [StringLength(500, ErrorMessage = "Las observaciones no pueden exceder 500 caracteres")]
        public string? Observaciones { get; set; }

        public EstadoPrestamo Estado { get; set; } = EstadoPrestamo.Activo;

        public DateTime FechaCreacion { get; set; } = DateTime.Now;

        public DateTime? FechaUltimaModificacion { get; set; }

        // Propiedades calculadas
        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoTotal { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalIntereses { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SaldoPendiente { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoPagado { get; set; }

        public int CuotasPagadas { get; set; }

        // Propiedades de navegación
        public virtual Cliente Cliente { get; set; } = null!;
        public virtual ICollection<Cuota> Cuotas { get; set; } = new List<Cuota>();
        public virtual ICollection<Pago> Pagos { get; set; } = new List<Pago>();

        // Propiedades adicionales para cálculos
        public decimal TasaInteresMensual => TasaInteresAnual / 12 / 100;
        
        public decimal PorcentajePagado => MontoTotal > 0 ? (MontoPagado / MontoTotal) * 100 : 0;
        
        public int CuotasPendientes => PlazoMeses - CuotasPagadas;
        
        public bool EstaVencido => DateTime.Now > FechaVencimiento && SaldoPendiente > 0;
    }

    public enum TipoAmortizacion
    {
        Francesa = 1,      // Cuotas constantes
        Alemana = 2,       // Capital constante
        Americana = 3,     // Solo intereses, capital al final
        Creciente = 4,     // Cuotas crecientes
        Personalizada = 5  // Definida por el usuario
    }

    public enum EstadoPrestamo
    {
        Activo = 1,
        Pagado = 2,
        Vencido = 3,
        Cancelado = 4,
        Reestructurado = 5
    }
}
