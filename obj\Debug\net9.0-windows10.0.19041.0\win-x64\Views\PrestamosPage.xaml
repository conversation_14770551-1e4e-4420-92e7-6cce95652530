﻿<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.PrestamosPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="💰 Gestión de Préstamos" FontSize="28" FontWeight="Bold" VerticalAlignment="Center"/>
            <Button Grid.Column="1" Content="➕ Nuevo Préstamo" Background="{ThemeResource SystemAccentColor}" 
                   Foreground="White" Padding="15,10" CornerRadius="5"/>
        </Grid>

        <!-- Search and Filters -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20" BorderBrush="LightGray" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" PlaceholderText="🔍 Buscar por cliente o número de préstamo..." 
                        Margin="0,0,10,0" VerticalAlignment="Center"/>
                <ComboBox Grid.Column="1" PlaceholderText="Estado" Width="120" Margin="0,0,10,0">
                    <ComboBoxItem Content="Todos"/>
                    <ComboBoxItem Content="Activo"/>
                    <ComboBoxItem Content="Pagado"/>
                    <ComboBoxItem Content="Vencido"/>
                    <ComboBoxItem Content="Cancelado"/>
                </ComboBox>
                <ComboBox Grid.Column="2" PlaceholderText="Período" Width="120" Margin="0,0,10,0">
                    <ComboBoxItem Content="Todos"/>
                    <ComboBoxItem Content="Este mes"/>
                    <ComboBoxItem Content="Últimos 3 meses"/>
                    <ComboBoxItem Content="Este año"/>
                </ComboBox>
                <Button Grid.Column="3" Content="🔍 Buscar" Padding="15,8"/>
            </Grid>
        </Border>

        <!-- Loans List -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" BorderBrush="LightGray" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <Border Grid.Row="0" Background="LightGray" Padding="15,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Cliente" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="Monto" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="Tasa %" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="Plazo" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="Saldo Pendiente" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="Estado" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="6" Text="Acciones" FontWeight="SemiBold"/>
                    </Grid>
                </Border>

                <!-- List Content -->
                <ListView Grid.Row="1" Padding="0">
                    <!-- Sample Data -->
                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Juan Pérez García" FontWeight="SemiBold"/>
                                <TextBlock Text="15/01/2024" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="1" Text="$50,000.00" VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBlock Grid.Column="2" Text="18.0%" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="24 meses" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$35,000.00" VerticalAlignment="Center" Foreground="Red"/>
                            <Border Grid.Column="5" Background="Green" CornerRadius="10" Padding="8,4" HorizontalAlignment="Center">
                                <TextBlock Text="Activo" Foreground="White" FontSize="12"/>
                            </Border>
                            
                            <StackPanel Grid.Column="6" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver detalles" Padding="8"/>
                                <Button Content="💳" ToolTipService.ToolTip="Registrar pago" Padding="8"/>
                                <Button Content="📊" ToolTipService.ToolTip="Ver amortización" Padding="8"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>

                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="María González López" FontWeight="SemiBold"/>
                                <TextBlock Text="20/02/2024" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="1" Text="$25,000.00" VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBlock Grid.Column="2" Text="15.5%" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="12 meses" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$8,500.00" VerticalAlignment="Center" Foreground="Orange"/>
                            <Border Grid.Column="5" Background="Orange" CornerRadius="10" Padding="8,4" HorizontalAlignment="Center">
                                <TextBlock Text="Vencido" Foreground="White" FontSize="12"/>
                            </Border>
                            
                            <StackPanel Grid.Column="6" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver detalles" Padding="8"/>
                                <Button Content="💳" ToolTipService.ToolTip="Registrar pago" Padding="8"/>
                                <Button Content="📊" ToolTipService.ToolTip="Ver amortización" Padding="8"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>

                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Carlos Rodríguez Martínez" FontWeight="SemiBold"/>
                                <TextBlock Text="10/03/2024" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="1" Text="$75,000.00" VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBlock Grid.Column="2" Text="20.0%" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="36 meses" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$0.00" VerticalAlignment="Center" Foreground="Green"/>
                            <Border Grid.Column="5" Background="Gray" CornerRadius="10" Padding="8,4" HorizontalAlignment="Center">
                                <TextBlock Text="Pagado" Foreground="White" FontSize="12"/>
                            </Border>
                            
                            <StackPanel Grid.Column="6" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver detalles" Padding="8"/>
                                <Button Content="📄" ToolTipService.ToolTip="Generar reporte" Padding="8"/>
                                <Button Content="📊" ToolTipService.ToolTip="Ver amortización" Padding="8"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>
                </ListView>
            </Grid>
        </Border>
    </Grid>
</Page>

