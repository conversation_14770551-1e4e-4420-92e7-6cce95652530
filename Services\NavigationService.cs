using Microsoft.UI.Xaml.Controls;

namespace DinPresto.Services
{
    public class NavigationService : INavigationService
    {
        private Frame? _frame;

        public Frame? Frame
        {
            get => _frame;
            set => _frame = value;
        }

        public bool CanGoBack => _frame?.CanGoBack ?? false;

        public void NavigateTo<T>() where T : Page
        {
            NavigateTo(typeof(T));
        }

        public void NavigateTo<T>(object parameter) where T : Page
        {
            NavigateTo(typeof(T), parameter);
        }

        public void NavigateTo(Type pageType)
        {
            if (_frame != null && pageType != null)
            {
                _frame.Navigate(pageType);
            }
        }

        public void NavigateTo(Type pageType, object parameter)
        {
            if (_frame != null && pageType != null)
            {
                _frame.Navigate(pageType, parameter);
            }
        }

        public void GoBack()
        {
            if (CanGoBack)
            {
                _frame?.GoBack();
            }
        }

        public void ClearBackStack()
        {
            _frame?.BackStack.Clear();
        }
    }
}
