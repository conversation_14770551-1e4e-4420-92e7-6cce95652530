using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DinPresto.Models
{
    public class Pago
    {
        public int Id { get; set; }

        [Required]
        public int PrestamoId { get; set; }

        public int? CuotaId { get; set; }

        [Required]
        public DateTime FechaPago { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoCapital { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoInteres { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoMora { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoTotal { get; set; }

        [Required]
        public TipoPago TipoPago { get; set; }

        [StringLength(100)]
        public string? NumeroReferencia { get; set; }

        [StringLength(200)]
        public string? Observaciones { get; set; }

        [Required]
        [StringLength(50)]
        public string UsuarioRegistro { get; set; } = "Sistema";

        public DateTime FechaRegistro { get; set; } = DateTime.Now;

        public bool Anulado { get; set; } = false;

        public DateTime? FechaAnulacion { get; set; }

        [StringLength(200)]
        public string? MotivoAnulacion { get; set; }

        // Propiedades de navegación
        public virtual Prestamo Prestamo { get; set; } = null!;
        public virtual Cuota? Cuota { get; set; }

        // Propiedades calculadas
        public string DescripcionTipoPago => TipoPago switch
        {
            TipoPago.Efectivo => "Efectivo",
            TipoPago.Transferencia => "Transferencia Bancaria",
            TipoPago.Cheque => "Cheque",
            TipoPago.TarjetaCredito => "Tarjeta de Crédito",
            TipoPago.TarjetaDebito => "Tarjeta de Débito",
            _ => "Otro"
        };
    }

    public enum TipoPago
    {
        Efectivo = 1,
        Transferencia = 2,
        Cheque = 3,
        TarjetaCredito = 4,
        TarjetaDebito = 5,
        Otro = 6
    }
}
