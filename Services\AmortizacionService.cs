using DinPresto.Models;

namespace DinPresto.Services
{
    public class AmortizacionService : IAmortizacionService
    {
        public List<Cuota> CalcularTablaAmortizacion(Prestamo prestamo)
        {
            var validacion = ValidarParametrosPrestamo(prestamo);
            if (!validacion.esValido)
            {
                throw new ArgumentException(validacion.mensaje);
            }

            return prestamo.TipoAmortizacion switch
            {
                TipoAmortizacion.Francesa => CalcularAmortizacionFrancesa(prestamo),
                TipoAmortizacion.Alemana => CalcularAmortizacionAlemana(prestamo),
                TipoAmortizacion.Americana => CalcularAmortizacionAmericana(prestamo),
                TipoAmortizacion.Creciente => CalcularAmortizacionCreciente(prestamo),
                TipoAmortizacion.Personalizada => CalcularAmortizacionPersonalizada(prestamo),
                _ => throw new NotSupportedException($"Tipo de amortización {prestamo.TipoAmortizacion} no soportado")
            };
        }

        public decimal CalcularMontoCuota(decimal capital, decimal tasaMensual, int plazoMeses, 
                                        TipoAmortizacion tipoAmortizacion, int numeroCuota = 1)
        {
            return tipoAmortizacion switch
            {
                TipoAmortizacion.Francesa => CalcularCuotaFrancesa(capital, tasaMensual, plazoMeses),
                TipoAmortizacion.Alemana => CalcularCuotaAlemana(capital, tasaMensual, plazoMeses, numeroCuota),
                TipoAmortizacion.Americana => CalcularCuotaAmericana(capital, tasaMensual, plazoMeses, numeroCuota),
                TipoAmortizacion.Creciente => CalcularCuotaCreciente(capital, tasaMensual, plazoMeses, numeroCuota),
                _ => 0
            };
        }

        public decimal CalcularTotalIntereses(Prestamo prestamo)
        {
            var cuotas = CalcularTablaAmortizacion(prestamo);
            return cuotas.Sum(c => c.MontoInteres);
        }

        public (bool esValido, string mensaje) ValidarParametrosPrestamo(Prestamo prestamo)
        {
            if (prestamo.MontoCapital <= 0)
                return (false, "El monto del capital debe ser mayor a 0");

            if (prestamo.TasaInteresAnual <= 0 || prestamo.TasaInteresAnual > 100)
                return (false, "La tasa de interés debe estar entre 0.01% y 100%");

            if (prestamo.PlazoMeses <= 0 || prestamo.PlazoMeses > 600)
                return (false, "El plazo debe estar entre 1 y 600 meses");

            if (prestamo.FechaInicio == default)
                return (false, "La fecha de inicio es requerida");

            return (true, string.Empty);
        }

        #region Métodos privados para cálculos específicos

        private List<Cuota> CalcularAmortizacionFrancesa(Prestamo prestamo)
        {
            var cuotas = new List<Cuota>();
            var tasaMensual = prestamo.TasaInteresMensual;
            var cuotaFija = CalcularCuotaFrancesa(prestamo.MontoCapital, tasaMensual, prestamo.PlazoMeses);
            var saldoCapital = prestamo.MontoCapital;
            var fechaCuota = prestamo.FechaInicio;

            for (int i = 1; i <= prestamo.PlazoMeses; i++)
            {
                fechaCuota = fechaCuota.AddMonths(1);
                var interes = saldoCapital * tasaMensual;
                var capital = cuotaFija - interes;
                saldoCapital -= capital;

                // Ajuste para la última cuota por redondeos
                if (i == prestamo.PlazoMeses && saldoCapital != 0)
                {
                    capital += saldoCapital;
                    saldoCapital = 0;
                }

                cuotas.Add(new Cuota
                {
                    PrestamoId = prestamo.Id,
                    NumeroCuota = i,
                    FechaVencimiento = fechaCuota,
                    MontoCapital = Math.Round(capital, 2),
                    MontoInteres = Math.Round(interes, 2),
                    MontoCuota = Math.Round(cuotaFija, 2),
                    SaldoCapital = Math.Round(Math.Max(0, saldoCapital), 2)
                });
            }

            return cuotas;
        }

        private List<Cuota> CalcularAmortizacionAlemana(Prestamo prestamo)
        {
            var cuotas = new List<Cuota>();
            var tasaMensual = prestamo.TasaInteresMensual;
            var capitalFijo = prestamo.MontoCapital / prestamo.PlazoMeses;
            var saldoCapital = prestamo.MontoCapital;
            var fechaCuota = prestamo.FechaInicio;

            for (int i = 1; i <= prestamo.PlazoMeses; i++)
            {
                fechaCuota = fechaCuota.AddMonths(1);
                var interes = saldoCapital * tasaMensual;
                var capital = capitalFijo;
                var cuota = capital + interes;
                saldoCapital -= capital;

                cuotas.Add(new Cuota
                {
                    PrestamoId = prestamo.Id,
                    NumeroCuota = i,
                    FechaVencimiento = fechaCuota,
                    MontoCapital = Math.Round(capital, 2),
                    MontoInteres = Math.Round(interes, 2),
                    MontoCuota = Math.Round(cuota, 2),
                    SaldoCapital = Math.Round(Math.Max(0, saldoCapital), 2)
                });
            }

            return cuotas;
        }

        private List<Cuota> CalcularAmortizacionAmericana(Prestamo prestamo)
        {
            var cuotas = new List<Cuota>();
            var tasaMensual = prestamo.TasaInteresMensual;
            var interesMensual = prestamo.MontoCapital * tasaMensual;
            var fechaCuota = prestamo.FechaInicio;

            for (int i = 1; i <= prestamo.PlazoMeses; i++)
            {
                fechaCuota = fechaCuota.AddMonths(1);
                var capital = i == prestamo.PlazoMeses ? prestamo.MontoCapital : 0;
                var interes = interesMensual;
                var cuota = capital + interes;
                var saldoCapital = i == prestamo.PlazoMeses ? 0 : prestamo.MontoCapital;

                cuotas.Add(new Cuota
                {
                    PrestamoId = prestamo.Id,
                    NumeroCuota = i,
                    FechaVencimiento = fechaCuota,
                    MontoCapital = Math.Round(capital, 2),
                    MontoInteres = Math.Round(interes, 2),
                    MontoCuota = Math.Round(cuota, 2),
                    SaldoCapital = Math.Round(saldoCapital, 2)
                });
            }

            return cuotas;
        }

        private List<Cuota> CalcularAmortizacionCreciente(Prestamo prestamo)
        {
            var cuotas = new List<Cuota>();
            var tasaMensual = prestamo.TasaInteresMensual;
            var capitalFijo = prestamo.MontoCapital / prestamo.PlazoMeses;
            var saldoCapital = prestamo.MontoCapital;
            var fechaCuota = prestamo.FechaInicio;
            var incrementoPorcentual = 0.02m; // 2% de incremento mensual

            for (int i = 1; i <= prestamo.PlazoMeses; i++)
            {
                fechaCuota = fechaCuota.AddMonths(1);
                var interes = saldoCapital * tasaMensual;
                var capital = capitalFijo * (1 + (incrementoPorcentual * (i - 1)));
                var cuota = capital + interes;
                saldoCapital -= capital;

                cuotas.Add(new Cuota
                {
                    PrestamoId = prestamo.Id,
                    NumeroCuota = i,
                    FechaVencimiento = fechaCuota,
                    MontoCapital = Math.Round(capital, 2),
                    MontoInteres = Math.Round(interes, 2),
                    MontoCuota = Math.Round(cuota, 2),
                    SaldoCapital = Math.Round(Math.Max(0, saldoCapital), 2)
                });
            }

            return cuotas;
        }

        private List<Cuota> CalcularAmortizacionPersonalizada(Prestamo prestamo)
        {
            // Para amortización personalizada, se puede implementar una lógica específica
            // Por ahora, usaremos la francesa como base
            return CalcularAmortizacionFrancesa(prestamo);
        }

        private decimal CalcularCuotaFrancesa(decimal capital, decimal tasaMensual, int plazoMeses)
        {
            if (tasaMensual == 0) return capital / plazoMeses;
            
            var factor = (decimal)Math.Pow((double)(1 + tasaMensual), plazoMeses);
            return capital * tasaMensual * factor / (factor - 1);
        }

        private decimal CalcularCuotaAlemana(decimal capital, decimal tasaMensual, int plazoMeses, int numeroCuota)
        {
            var capitalFijo = capital / plazoMeses;
            var saldoCapital = capital - (capitalFijo * (numeroCuota - 1));
            var interes = saldoCapital * tasaMensual;
            return capitalFijo + interes;
        }

        private decimal CalcularCuotaAmericana(decimal capital, decimal tasaMensual, int plazoMeses, int numeroCuota)
        {
            var interes = capital * tasaMensual;
            return numeroCuota == plazoMeses ? capital + interes : interes;
        }

        private decimal CalcularCuotaCreciente(decimal capital, decimal tasaMensual, int plazoMeses, int numeroCuota)
        {
            var capitalFijo = capital / plazoMeses;
            var incrementoPorcentual = 0.02m;
            var capitalCuota = capitalFijo * (1 + (incrementoPorcentual * (numeroCuota - 1)));
            var saldoCapital = capital - (capitalFijo * (numeroCuota - 1));
            var interes = saldoCapital * tasaMensual;
            return capitalCuota + interes;
        }

        #endregion
    }
}
