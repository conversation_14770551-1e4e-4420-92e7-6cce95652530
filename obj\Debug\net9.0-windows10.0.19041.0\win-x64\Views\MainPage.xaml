﻿<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.MainPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:DinPresto"
    xmlns:viewmodels="using:DinPresto.ViewModels"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{ThemeResource SystemAccentColor}" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <FontIcon Glyph="&#xE8AB;" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="DinPresto" FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Text="Sistema de Gestión de Préstamos" FontSize="14" Foreground="White"
                              Opacity="0.8" VerticalAlignment="Center" Margin="20,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🔄 Actualizar" Margin="0,0,10,0"/>
                    <Ellipse Width="32" Height="32" Fill="White"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <Border Grid.Column="0" Background="LightGray"
                   BorderBrush="Gray" BorderThickness="0,0,1,0">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock Text="NAVEGACIÓN" FontSize="12" FontWeight="SemiBold"
                                  Foreground="DarkGray" Margin="10,10,10,5"/>

                        <Button x:ConnectionId='3' x:Name="DashboardButton" Content="📊 Dashboard" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"                              />

                        <Button x:ConnectionId='4' x:Name="ClientesButton" Content="👥 Clientes" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"                             />

                        <Button x:ConnectionId='5' x:Name="PrestamosButton" Content="💰 Préstamos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"                              />

                        <Button x:ConnectionId='6' x:Name="PagosButton" Content="💳 Pagos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"                          />

                        <Button x:ConnectionId='7' x:Name="ReportesButton" Content="📄 Reportes" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"                             />

                        <Rectangle Height="1" Fill="Gray" Margin="0,10"/>

                        <Button x:ConnectionId='8' x:Name="ConfiguracionButton" Content="⚙️ Configuración" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"                                  />
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Content Area with Frame for Navigation -->
            <Frame x:ConnectionId='2' x:Name="ContentFrame" Grid.Column="1"/>
        </Grid>
    </Grid>
</Page>

