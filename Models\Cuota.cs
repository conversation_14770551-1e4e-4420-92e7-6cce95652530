using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DinPresto.Models
{
    public class Cuota
    {
        public int Id { get; set; }

        [Required]
        public int PrestamoId { get; set; }

        [Required]
        public int NumeroCuota { get; set; }

        [Required]
        public DateTime FechaVencimiento { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoCapital { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoInteres { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MontoCuota { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SaldoCapital { get; set; }

        public EstadoCuota Estado { get; set; } = EstadoCuota.Pendiente;

        public DateTime? FechaPago { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MontoPagado { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MontoMora { get; set; }

        public int? DiasAtraso { get; set; }

        [StringLength(200)]
        public string? Observaciones { get; set; }

        // Propiedades de navegación
        public virtual Prestamo Prestamo { get; set; } = null!;
        public virtual ICollection<Pago> Pagos { get; set; } = new List<Pago>();

        // Propiedades calculadas
        public bool EstaVencida => DateTime.Now > FechaVencimiento && Estado == EstadoCuota.Pendiente;
        
        public int DiasVencimiento => EstaVencida ? (DateTime.Now - FechaVencimiento).Days : 0;
        
        public decimal MontoTotalConMora => MontoCuota + (MontoMora ?? 0);
        
        public decimal SaldoPendiente => Estado == EstadoCuota.Pagada ? 0 : MontoTotalConMora - (MontoPagado ?? 0);
    }

    public enum EstadoCuota
    {
        Pendiente = 1,
        Pagada = 2,
        PagoParcial = 3,
        Vencida = 4,
        Condonada = 5
    }
}
