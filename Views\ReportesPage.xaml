<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.ReportesPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ScrollViewer Padding="20">
        <StackPanel Spacing="20">
            <!-- Header -->
            <TextBlock Text="📄 Reportes y Análisis" FontSize="28" FontWeight="Bold"/>

            <!-- Report Categories -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Financial Reports -->
                <Border Grid.Column="0" Grid.Row="0" Background="White" CornerRadius="8" Padding="20" 
                       Margin="0,0,10,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel Spacing="15">
                        <Grid>
                            <TextBlock Text="💰 Reportes Financieros" FontSize="18" FontWeight="SemiBold" HorizontalAlignment="Left"/>
                            <FontIcon Glyph="&#xE8AB;" FontSize="24" Foreground="{ThemeResource SystemAccentColor}" HorizontalAlignment="Right"/>
                        </Grid>
                        
                        <StackPanel Spacing="8">
                            <Button Content="📊 Estado de Cartera" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="💵 Ingresos por Período" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="📈 Análisis de Rentabilidad" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="💸 Flujo de Caja" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Client Reports -->
                <Border Grid.Column="1" Grid.Row="0" Background="White" CornerRadius="8" Padding="20" 
                       Margin="10,0,0,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel Spacing="15">
                        <Grid>
                            <TextBlock Text="👥 Reportes de Clientes" FontSize="18" FontWeight="SemiBold" HorizontalAlignment="Left"/>
                            <FontIcon Glyph="&#xE716;" FontSize="24" Foreground="Green" HorizontalAlignment="Right"/>
                        </Grid>
                        
                        <StackPanel Spacing="8">
                            <Button Content="📋 Lista de Clientes" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="⚠️ Clientes Morosos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="⭐ Mejores Clientes" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="📞 Contactos Pendientes" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Loan Reports -->
                <Border Grid.Column="0" Grid.Row="1" Background="White" CornerRadius="8" Padding="20" 
                       Margin="0,0,10,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel Spacing="15">
                        <Grid>
                            <TextBlock Text="🏦 Reportes de Préstamos" FontSize="18" FontWeight="SemiBold" HorizontalAlignment="Left"/>
                            <FontIcon Glyph="&#xE8C7;" FontSize="24" Foreground="Blue" HorizontalAlignment="Right"/>
                        </Grid>
                        
                        <StackPanel Spacing="8">
                            <Button Content="📄 Préstamos Activos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="⏰ Préstamos Vencidos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="✅ Préstamos Pagados" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="📅 Vencimientos Próximos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Payment Reports -->
                <Border Grid.Column="1" Grid.Row="1" Background="White" CornerRadius="8" Padding="20" 
                       Margin="10,0,0,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel Spacing="15">
                        <Grid>
                            <TextBlock Text="💳 Reportes de Pagos" FontSize="18" FontWeight="SemiBold" HorizontalAlignment="Left"/>
                            <FontIcon Glyph="&#xE8C8;" FontSize="24" Foreground="Purple" HorizontalAlignment="Right"/>
                        </Grid>
                        
                        <StackPanel Spacing="8">
                            <Button Content="💰 Pagos por Período" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="📊 Métodos de Pago" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="📈 Tendencias de Pago" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                            <Button Content="🧾 Recibos Emitidos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Padding="10,8"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Custom Reports -->
                <Border Grid.Column="0" Grid.Row="2" Grid.ColumnSpan="2" Background="White" CornerRadius="8" Padding="20" 
                       Margin="0,0,0,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel Spacing="15">
                        <Grid>
                            <TextBlock Text="🔧 Reportes Personalizados" FontSize="18" FontWeight="SemiBold" HorizontalAlignment="Left"/>
                            <FontIcon Glyph="&#xE713;" FontSize="24" Foreground="Orange" HorizontalAlignment="Right"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Spacing="10">
                                <TextBlock Text="Período:" FontWeight="SemiBold"/>
                                <ComboBox Width="150" HorizontalAlignment="Left">
                                    <ComboBoxItem Content="Este mes"/>
                                    <ComboBoxItem Content="Últimos 3 meses"/>
                                    <ComboBoxItem Content="Este año"/>
                                    <ComboBoxItem Content="Personalizado"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Spacing="10">
                                <TextBlock Text="Tipo de Reporte:" FontWeight="SemiBold"/>
                                <ComboBox Width="150" HorizontalAlignment="Left">
                                    <ComboBoxItem Content="Resumen Ejecutivo"/>
                                    <ComboBoxItem Content="Detallado"/>
                                    <ComboBoxItem Content="Por Cliente"/>
                                    <ComboBoxItem Content="Por Préstamo"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Spacing="10">
                                <TextBlock Text="Formato:" FontWeight="SemiBold"/>
                                <ComboBox Width="120" HorizontalAlignment="Left">
                                    <ComboBoxItem Content="PDF"/>
                                    <ComboBoxItem Content="Excel"/>
                                    <ComboBoxItem Content="Word"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Column="3" Spacing="10" VerticalAlignment="Bottom">
                                <Button Content="📊 Generar Reporte" Background="{ThemeResource SystemAccentColor}" 
                                       Foreground="White" Padding="15,10" HorizontalAlignment="Left"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Quick Stats -->
            <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="LightGray" BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock Text="📈 Estadísticas Rápidas" FontSize="18" FontWeight="SemiBold"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="$125,000" FontSize="24" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center"/>
                            <TextBlock Text="Ingresos Este Mes" FontSize="12" Foreground="Gray" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="89%" FontSize="24" FontWeight="Bold" Foreground="Blue" HorizontalAlignment="Center"/>
                            <TextBlock Text="Tasa de Cobro" FontSize="12" Foreground="Gray" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="15" FontSize="24" FontWeight="Bold" Foreground="Orange" HorizontalAlignment="Center"/>
                            <TextBlock Text="Préstamos Nuevos" FontSize="12" Foreground="Gray" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                            <TextBlock Text="3.2%" FontSize="24" FontWeight="Bold" Foreground="Red" HorizontalAlignment="Center"/>
                            <TextBlock Text="Tasa de Morosidad" FontSize="12" Foreground="Gray" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</Page>
