<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.DashboardPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ScrollViewer Padding="20">
        <StackPanel Spacing="20">
            <!-- Dashboard Title -->
            <TextBlock Text="Dashboard" FontSize="28" FontWeight="Bold"/>

            <!-- Dashboard Cards -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Card 1: Total Clientes -->
                <Border Grid.Column="0" Grid.Row="0" Background="White"
                       CornerRadius="8" Padding="20" Margin="0,0,10,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel>
                        <Grid>
                            <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Left"/>
                            <TextBlock Text="125" FontSize="32" FontWeight="Bold" HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="Total Clientes" FontSize="14" Foreground="Gray" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Card 2: Total Préstamos -->
                <Border Grid.Column="1" Grid.Row="0" Background="White"
                       CornerRadius="8" Padding="20" Margin="0,0,10,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel>
                        <Grid>
                            <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Left"/>
                            <TextBlock Text="89" FontSize="32" FontWeight="Bold" HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="Total Préstamos" FontSize="14" Foreground="Gray" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Card 3: Préstamos Activos -->
                <Border Grid.Column="2" Grid.Row="0" Background="White"
                       CornerRadius="8" Padding="20" Margin="0,0,10,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel>
                        <Grid>
                            <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Left"/>
                            <TextBlock Text="67" FontSize="32" FontWeight="Bold" HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="Préstamos Activos" FontSize="14" Foreground="Gray" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Card 4: Monto Total Pendiente -->
                <Border Grid.Column="3" Grid.Row="0" Background="White"
                       CornerRadius="8" Padding="20" Margin="0,0,10,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel>
                        <Grid>
                            <TextBlock Text="💸" FontSize="24" HorizontalAlignment="Left"/>
                            <TextBlock Text="$45,000.00" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="Monto Pendiente" FontSize="14" Foreground="Gray" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Card 5: Cuotas Vencidas -->
                <Border Grid.Column="0" Grid.Row="1" Background="White"
                       CornerRadius="8" Padding="20" Margin="0,0,10,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel>
                        <Grid>
                            <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Left"/>
                            <TextBlock Text="5" FontSize="32" FontWeight="Bold" HorizontalAlignment="Right" Foreground="Red"/>
                        </Grid>
                        <TextBlock Text="Cuotas Vencidas" FontSize="14" Foreground="Gray" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Card 6: Monto Total Prestado -->
                <Border Grid.Column="1" Grid.Row="1" Background="White"
                       CornerRadius="8" Padding="20" Margin="0,0,10,10" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel>
                        <Grid>
                            <TextBlock Text="💵" FontSize="24" HorizontalAlignment="Left"/>
                            <TextBlock Text="$150,000.00" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="Green"/>
                        </Grid>
                        <TextBlock Text="Monto Total Prestado" FontSize="14" Foreground="Gray" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Recent Activity Section -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Préstamos Recientes -->
                <Border Grid.Column="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,10,0" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="Préstamos Recientes" FontSize="18" FontWeight="SemiBold" Margin="0,0,0,15"/>
                        <StackPanel>
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Juan Pérez García" FontWeight="SemiBold"/>
                                    <TextBlock Text="15/12/2024" FontSize="12" Foreground="Gray"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Text="$50,000.00" FontWeight="SemiBold" VerticalAlignment="Center"/>
                            </Grid>
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="María González López" FontWeight="SemiBold"/>
                                    <TextBlock Text="10/12/2024" FontSize="12" Foreground="Gray"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Text="$25,000.00" FontWeight="SemiBold" VerticalAlignment="Center"/>
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Cuotas Próximas a Vencer -->
                <Border Grid.Column="1" Background="White" CornerRadius="8" Padding="20" Margin="10,0,0,0" BorderBrush="LightGray" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="Cuotas Próximas a Vencer" FontSize="18" FontWeight="SemiBold" Margin="0,0,0,15"/>
                        <StackPanel>
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Carlos Rodríguez" FontWeight="SemiBold"/>
                                    <TextBlock Text="Vence: 20/12/2024" FontSize="12" Foreground="Orange"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Text="$2,850.00" FontWeight="SemiBold" VerticalAlignment="Center"/>
                            </Grid>
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Ana Martínez" FontWeight="SemiBold"/>
                                    <TextBlock Text="Vence: 22/12/2024" FontSize="12" Foreground="Orange"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Text="$1,200.00" FontWeight="SemiBold" VerticalAlignment="Center"/>
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Refresh Button -->
            <Button Content="🔄 Actualizar Datos" HorizontalAlignment="Center" Padding="20,10"/>
        </StackPanel>
    </ScrollViewer>
</Page>
