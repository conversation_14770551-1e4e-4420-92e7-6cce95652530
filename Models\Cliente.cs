using System.ComponentModel.DataAnnotations;

namespace DinPresto.Models
{
    public class Cliente
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "El nombre es obligatorio")]
        [StringLength(100, ErrorMessage = "El nombre no puede exceder 100 caracteres")]
        public string Nombre { get; set; } = string.Empty;

        [Required(ErrorMessage = "Los apellidos son obligatorios")]
        [StringLength(100, ErrorMessage = "Los apellidos no pueden exceder 100 caracteres")]
        public string Apellidos { get; set; } = string.Empty;

        [Required(ErrorMessage = "El documento de identidad es obligatorio")]
        [StringLength(20, ErrorMessage = "El documento no puede exceder 20 caracteres")]
        public string DocumentoIdentidad { get; set; } = string.Empty;

        public TipoDocumento TipoDocumento { get; set; }

        [EmailAddress(ErrorMessage = "El formato del email no es válido")]
        [StringLength(100, ErrorMessage = "El email no puede exceder 100 caracteres")]
        public string? Email { get; set; }

        [Phone(ErrorMessage = "El formato del teléfono no es válido")]
        [StringLength(20, ErrorMessage = "El teléfono no puede exceder 20 caracteres")]
        public string? Telefono { get; set; }

        [StringLength(200, ErrorMessage = "La dirección no puede exceder 200 caracteres")]
        public string? Direccion { get; set; }

        public DateTime FechaRegistro { get; set; } = DateTime.Now;

        public bool Activo { get; set; } = true;

        // Propiedades de navegación
        public virtual ICollection<Prestamo> Prestamos { get; set; } = new List<Prestamo>();

        // Propiedades calculadas
        public string NombreCompleto => $"{Nombre} {Apellidos}";
        
        public int TotalPrestamos => Prestamos?.Count ?? 0;
        
        public decimal TotalDeuda => Prestamos?.Where(p => p.Estado == EstadoPrestamo.Activo)
                                              .Sum(p => p.SaldoPendiente) ?? 0;
    }

    public enum TipoDocumento
    {
        Cedula = 1,
        Pasaporte = 2,
        RNC = 3,
        Otro = 4
    }
}
