using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DinPresto.Models;
using DinPresto.Services;
using Microsoft.EntityFrameworkCore;
using DinPresto.Data;
using System.Collections.ObjectModel;

namespace DinPresto.ViewModels
{
    public partial class MainViewModel : BaseViewModel
    {
        private readonly DinPrestoContext _context;
        private readonly IAmortizacionService _amortizacionService;

        [ObservableProperty]
        private string selectedMenuItem = "Dashboard";

        [ObservableProperty]
        private int totalClientes;

        [ObservableProperty]
        private int totalPrestamos;

        [ObservableProperty]
        private int prestamosActivos;

        [ObservableProperty]
        private decimal montoTotalPrestado;

        [ObservableProperty]
        private decimal montoTotalPendiente;

        [ObservableProperty]
        private int cuotasVencidas;

        public ObservableCollection<Prestamo> PrestamosRecientes { get; } = new();
        public ObservableCollection<Cuota> CuotasProximasVencer { get; } = new();

        public MainViewModel(DinPrestoContext context, IAmortizacionService amortizacionService)
        {
            _context = context;
            _amortizacionService = amortizacionService;
            Title = "DinPresto - Sistema de Gestión de Préstamos";
            
            // Cargar datos iniciales
            _ = LoadDashboardDataAsync();
        }

        [RelayCommand]
        private async Task LoadDashboardDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                // Cargar estadísticas generales
                TotalClientes = await _context.Clientes.CountAsync(c => c.Activo);
                TotalPrestamos = await _context.Prestamos.CountAsync();
                PrestamosActivos = await _context.Prestamos.CountAsync(p => p.Estado == EstadoPrestamo.Activo);
                
                MontoTotalPrestado = await _context.Prestamos
                    .Where(p => p.Estado == EstadoPrestamo.Activo)
                    .SumAsync(p => p.MontoCapital);
                
                MontoTotalPendiente = await _context.Prestamos
                    .Where(p => p.Estado == EstadoPrestamo.Activo)
                    .SumAsync(p => p.SaldoPendiente);

                CuotasVencidas = await _context.Cuotas
                    .CountAsync(c => c.Estado == EstadoCuota.Vencida);

                // Cargar préstamos recientes
                var prestamosRecientes = await _context.Prestamos
                    .Include(p => p.Cliente)
                    .OrderByDescending(p => p.FechaCreacion)
                    .Take(10)
                    .ToListAsync();

                PrestamosRecientes.Clear();
                foreach (var prestamo in prestamosRecientes)
                {
                    PrestamosRecientes.Add(prestamo);
                }

                // Cargar cuotas próximas a vencer
                var fechaLimite = DateTime.Now.AddDays(30);
                var cuotasProximas = await _context.Cuotas
                    .Include(c => c.Prestamo)
                    .ThenInclude(p => p.Cliente)
                    .Where(c => c.Estado == EstadoCuota.Pendiente && 
                               c.FechaVencimiento <= fechaLimite)
                    .OrderBy(c => c.FechaVencimiento)
                    .Take(10)
                    .ToListAsync();

                CuotasProximasVencer.Clear();
                foreach (var cuota in cuotasProximas)
                {
                    CuotasProximasVencer.Add(cuota);
                }

            }, "Cargando datos del dashboard...");
        }

        [RelayCommand]
        private void NavigateToClientes()
        {
            SelectedMenuItem = "Clientes";
            // Aquí se implementaría la navegación real
        }

        [RelayCommand]
        private void NavigateToPrestamos()
        {
            SelectedMenuItem = "Prestamos";
            // Aquí se implementaría la navegación real
        }

        [RelayCommand]
        private void NavigateToPagos()
        {
            SelectedMenuItem = "Pagos";
            // Aquí se implementaría la navegación real
        }

        [RelayCommand]
        private void NavigateToReportes()
        {
            SelectedMenuItem = "Reportes";
            // Aquí se implementaría la navegación real
        }

        [RelayCommand]
        private void NavigateToConfiguracion()
        {
            SelectedMenuItem = "Configuracion";
            // Aquí se implementaría la navegación real
        }

        [RelayCommand]
        private async Task RefreshDataAsync()
        {
            await LoadDashboardDataAsync();
        }

        // Propiedades calculadas para el dashboard
        public string MontoTotalPrestadoFormatted => MontoTotalPrestado.ToString("C2");
        public string MontoTotalPendienteFormatted => MontoTotalPendiente.ToString("C2");
        public decimal PorcentajePagado => MontoTotalPrestado > 0 ? 
            ((MontoTotalPrestado - MontoTotalPendiente) / MontoTotalPrestado) * 100 : 0;
        public string PorcentajePagadoFormatted => $"{PorcentajePagado:F1}%";
    }
}
