{"format": 1, "restore": {"D:\\Software3\\Dinpresto\\DinPresto.csproj": {}}, "projects": {"D:\\Software3\\Dinpresto\\DinPresto.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Software3\\Dinpresto\\DinPresto.csproj", "projectName": "DinPresto", "projectPath": "D:\\Software3\\Dinpresto\\DinPresto.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Software3\\Dinpresto\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.9, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.Web.WebView2": {"target": "Package", "version": "[1.*, )"}, "Microsoft.Windows.SDK.BuildTools": {"target": "Package", "version": "[10.*, )"}, "Microsoft.WindowsAppSDK": {"target": "Package", "version": "[1.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}