﻿<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.ClientesPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="👥 Gestión de Clientes" FontSize="28" FontWeight="Bold" VerticalAlignment="Center"/>
            <Button Grid.Column="1" Content="➕ Nuevo Cliente" Background="{ThemeResource SystemAccentColor}" 
                   Foreground="White" Padding="15,10" CornerRadius="5"/>
        </Grid>

        <!-- Search and Filters -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20" BorderBrush="LightGray" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" PlaceholderText="🔍 Buscar por nombre, documento o teléfono..." 
                        Margin="0,0,10,0" VerticalAlignment="Center"/>
                <ComboBox Grid.Column="1" PlaceholderText="Tipo Documento" Width="150" Margin="0,0,10,0">
                    <ComboBoxItem Content="Todos"/>
                    <ComboBoxItem Content="Cédula"/>
                    <ComboBoxItem Content="Pasaporte"/>
                    <ComboBoxItem Content="RNC"/>
                </ComboBox>
                <Button Grid.Column="2" Content="🔍 Buscar" Padding="15,8"/>
            </Grid>
        </Border>

        <!-- Clients List -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" BorderBrush="LightGray" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <Border Grid.Row="0" Background="LightGray" Padding="15,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Cliente" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="Documento" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="Teléfono" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="Préstamos" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="Deuda Total" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="Acciones" FontWeight="SemiBold"/>
                    </Grid>
                </Border>

                <!-- List Content -->
                <ListView Grid.Row="1" Padding="0">
                    <!-- Sample Data - This will be bound to ViewModel -->
                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Juan Pérez García" FontWeight="SemiBold"/>
                                <TextBlock Text="<EMAIL>" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="1" Text="001-1234567-8" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="(809) 123-4567" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="2" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$15,000.00" VerticalAlignment="Center" Foreground="Red"/>
                            
                            <StackPanel Grid.Column="5" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver detalles" Padding="8"/>
                                <Button Content="✏️" ToolTipService.ToolTip="Editar" Padding="8"/>
                                <Button Content="🗑️" ToolTipService.ToolTip="Eliminar" Padding="8" Foreground="Red"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>

                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="María González López" FontWeight="SemiBold"/>
                                <TextBlock Text="<EMAIL>" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="1" Text="001-9876543-2" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="(829) 987-6543" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="1" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$8,500.00" VerticalAlignment="Center" Foreground="Orange"/>
                            
                            <StackPanel Grid.Column="5" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver detalles" Padding="8"/>
                                <Button Content="✏️" ToolTipService.ToolTip="Editar" Padding="8"/>
                                <Button Content="🗑️" ToolTipService.ToolTip="Eliminar" Padding="8" Foreground="Red"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>

                    <ListViewItem>
                        <Grid Padding="15,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Carlos Rodríguez Martínez" FontWeight="SemiBold"/>
                                <TextBlock Text="<EMAIL>" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <TextBlock Grid.Column="1" Text="001-5555555-5" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="(849) 555-5555" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="0" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="$0.00" VerticalAlignment="Center" Foreground="Green"/>
                            
                            <StackPanel Grid.Column="5" Orientation="Horizontal" Spacing="5">
                                <Button Content="👁️" ToolTipService.ToolTip="Ver detalles" Padding="8"/>
                                <Button Content="✏️" ToolTipService.ToolTip="Editar" Padding="8"/>
                                <Button Content="🗑️" ToolTipService.ToolTip="Eliminar" Padding="8" Foreground="Red"/>
                            </StackPanel>
                        </Grid>
                    </ListViewItem>
                </ListView>
            </Grid>
        </Border>
    </Grid>
</Page>

