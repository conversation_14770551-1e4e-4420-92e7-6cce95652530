using Microsoft.UI.Xaml.Controls;

namespace DinPresto.Services
{
    public interface INavigationService
    {
        Frame? Frame { get; set; }
        bool CanGoBack { get; }
        void NavigateTo<T>() where T : Page;
        void NavigateTo<T>(object parameter) where T : Page;
        void NavigateTo(Type pageType);
        void NavigateTo(Type pageType, object parameter);
        void GoBack();
        void ClearBackStack();
    }
}
