{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"DinPresto/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.9", "Microsoft.EntityFrameworkCore.Tools": "9.0.9", "Microsoft.Extensions.Hosting": "9.0.9", "Microsoft.Web.WebView2": "1.0.3485.44", "Microsoft.Windows.SDK.BuildTools": "10.0.26100.4948", "Microsoft.WindowsAppSDK": "1.8.250907003", "Microsoft.Web.WebView2.Core.Projection": "1.0.3485.44", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.19041.57"}, "runtime": {"DinPresto.dll": {}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.57": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.19041.38", "fileVersion": "10.0.19041.55"}, "WinRT.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.48161"}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "********", "fileVersion": "2.14.1.48190"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.Sqlite.Core/9.0.9": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore/9.0.9": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.9", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.9": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.9": {}, "Microsoft.EntityFrameworkCore.Design/9.0.9": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyModel": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.9": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.9": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyModel": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.9"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.9": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.9", "Microsoft.EntityFrameworkCore.Relational": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyModel": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.9": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.9"}}, "Microsoft.Extensions.Caching.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Caching.Memory/9.0.9": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Physical": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.Json/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.Json": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Physical": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.DependencyInjection/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.9": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.DependencyModel/9.0.9": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.9", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Diagnostics/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.9", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.9": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileSystemGlobbing": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.9": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Hosting/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.Binder": "9.0.9", "Microsoft.Extensions.Configuration.CommandLine": "9.0.9", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.9", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.9", "Microsoft.Extensions.Configuration.Json": "9.0.9", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.9", "Microsoft.Extensions.DependencyInjection": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Diagnostics": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Physical": "9.0.9", "Microsoft.Extensions.Hosting.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Configuration": "9.0.9", "Microsoft.Extensions.Logging.Console": "9.0.9", "Microsoft.Extensions.Logging.Debug": "9.0.9", "Microsoft.Extensions.Logging.EventLog": "9.0.9", "Microsoft.Extensions.Logging.EventSource": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.Binder": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging.Console/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Configuration": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging.Debug/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "System.Diagnostics.EventLog": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Options/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.9": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.Binder": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Primitives/9.0.9": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Web.WebView2/1.0.3485.44": {"native": {"runtimes/win-x64/native/WebView2Loader.dll": {"fileVersion": "1.0.3485.44"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {}, "Microsoft.WindowsAppSDK/1.8.250907003": {"dependencies": {"Microsoft.WindowsAppSDK.AI": "1.8.37", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.DWrite": "1.8.25090401", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004", "Microsoft.WindowsAppSDK.Runtime": "1.8.250907003", "Microsoft.WindowsAppSDK.Widgets": "1.8.250904007", "Microsoft.WindowsAppSDK.WinUI": "1.8.250906003"}}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002"}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}}}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.26100.4948", "Microsoft.Windows.SDK.BuildTools.MSIX": "1.7.20250829.1"}}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}}, "native": {"runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"fileVersion": "1.8.0.0"}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"fileVersion": "1.8.0.0"}}}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.27108.1004"}}}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"dependencies": {"Microsoft.Web.WebView2": "1.0.3485.44", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.2509"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.1"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"native": {"runtimes/win-x64/native/e_sqlite3.dll": {"fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/7.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.EventLog/9.0.9": {"runtime": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.IO.Pipelines/7.0.0": {}, "System.Memory/4.5.3": {}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Json/9.0.9": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Threading.Channels/7.0.0": {}, "Microsoft.Web.WebView2.Core.Projection/1.0.3485.44": {"runtime": {"Microsoft.Web.WebView2.Core.Projection.dll": {"assemblyVersion": "1.0.3485.44", "fileVersion": "1.0.3485.44"}}}}}, "libraries": {"DinPresto/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.57": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-DjxZRueHp0qvZxhvW+H1IWYkSofZI8Chg710KYJjNP/6S4q3rt97pvR8AHOompkSwaN92VLKz5uw01iUt85cMg==", "path": "microsoft.data.sqlite.core/9.0.9", "hashPath": "microsoft.data.sqlite.core.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-zkt5yQgnpWKX3rOxn+ZcV23Aj0296XCTqg4lx1hKY+wMXBgkn377UhBrY/A4H6kLpNT7wqZN98xCV0YHXu9VRA==", "path": "microsoft.entityframeworkcore/9.0.9", "hashPath": "microsoft.entityframeworkcore.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-QdM2k3Mnip2QsaxJbCI95dc2SajRMENdmaMhVKj4jPC5dmkoRcu3eEdvZAgDbd4bFVV1jtPGdHtXewtoBMlZqA==", "path": "microsoft.entityframeworkcore.abstractions/9.0.9", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-uiKeU/qR0YpaDUa4+g0rAjKCuwfq8YWZGcpPptnFWIr1K7dXQTm/15D2HDwwU4ln3Uf66krYybymuY58ua4hhw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.9", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-cFx<PERSON>70tohWe3ugCjLhZB01mR7WHpg5dEK6zHsbkDFfpLxWT+HoZQKgchTJgF4bPWBPTyrlYlqfPY212fFtmJjg==", "path": "microsoft.entityframeworkcore.design/9.0.9", "hashPath": "microsoft.entityframeworkcore.design.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-SonFU9a8x4jZIhIBtCw1hIE3QKjd4c7Y3mjptoh682dfQe7K9pUPGcEV/sk4n8AJdq4fkyJPCaOdYaObhae/Iw==", "path": "microsoft.entityframeworkcore.relational/9.0.9", "hashPath": "microsoft.entityframeworkcore.relational.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-SiAd32IMTAQDo+jQt5GAzCq+5qI/OEdsrbW0qEDr0hUEAh3jnRlt0gbZgDGDUtWk5SWITufB6AOZi0qet9dJIw==", "path": "microsoft.entityframeworkcore.sqlite/9.0.9", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-eQVF8fBgDxjnjan3EB1ysdfDO7lKKfWKTT4VR0BInU4Mi6ADdgiOdm6qvZ/ufh04f3hhPL5lyknx5XotGzBh8A==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.9", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-Q8n1PXXJApa1qX8HI3r/YuHoJ1HuLwjI2hLqaCV9K9pqQhGpi6Z38laOYwL2ElUOTWCxTKMDEMMYWfPlw6rwgg==", "path": "microsoft.entityframeworkcore.tools/9.0.9", "hashPath": "microsoft.entityframeworkcore.tools.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-NgtRHOdPrAEacfjXLSrH/SRrSqGf6Vaa6d16mW2yoyJdg7AJr0BnBvxkv7PkCm/CHVyzojTK7Y+oUDEulqY1Qw==", "path": "microsoft.extensions.caching.abstractions/9.0.9", "hashPath": "microsoft.extensions.caching.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-ln31BtsDsBQxykJgxuCtiUXWRET9FmqeEq0BpPIghkYtGpDDVs8ZcLHAjCCzbw6aGoLek4Z7JaDjSO/CjOD0iw==", "path": "microsoft.extensions.caching.memory/9.0.9", "hashPath": "microsoft.extensions.caching.memory.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-w87wF/90/VI0ZQBhf4rbMEeyEy0vi2WKjFmACsNAKNaorY+ZlVz7ddyXkbADvaWouMKffNmR0yQOGcrvSSvKGg==", "path": "microsoft.extensions.configuration/9.0.9", "hashPath": "microsoft.extensions.configuration.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-p5RKAY9POvs3axwA/AQRuJeM8AHuE8h4qbP1NxQeGm0ep46aXz1oCLAp/oOYxX1GsjStgdhHrN3XXLLXr0+b3w==", "path": "microsoft.extensions.configuration.abstractions/9.0.9", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-6SIp/6Bngk4jm2W36JekZbiIbFPdE/eMUtrJEqIqHGpd1zar3jvgnwxnpWQfzUiGrkyY8q8s6V82zkkEZozghA==", "path": "microsoft.extensions.configuration.binder/9.0.9", "hashPath": "microsoft.extensions.configuration.binder.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-9bzGOcHoTi8ijrj0MHh5qUY6n9CuittZUqEOj5iE0ZJoSCfG0BI9nhcpd8MC9bOOgjZW5OeizKO8rgta9lSVyA==", "path": "microsoft.extensions.configuration.commandline/9.0.9", "hashPath": "microsoft.extensions.configuration.commandline.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-AB8suTh4STAMGDkPer5vL0YNp09eplvbkIbOfFJ1z8D1zOiFF8Hipk9FhCLU4Ea6TosWmGrK30ZIUO9KvAeFcg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.9", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-fvgubCs++wTowHWuQ5TAyZV0S6ldA59U+tBVqFr4/WLd0oEf6ESbdBN2CFaVdn4sZqnarqMnl2O3++RG/Jrf/w==", "path": "microsoft.extensions.configuration.fileextensions/9.0.9", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-PiPYo1GTinR2ECM80zYdZUIFmde6jj5DryXUcOJg3yIjh+KQMQr42e+COD03QUsUiqNkJk511wVTnVpTm2AVZA==", "path": "microsoft.extensions.configuration.json/9.0.9", "hashPath": "microsoft.extensions.configuration.json.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-bFaNxfU8gQJX3K/Dd6XT0YIJ5ZVihdAY6Z02p2nVTUHjUsaWflLIucZOgB/ecSNnN3zbbBEf1oFC7q5NHTZIHw==", "path": "microsoft.extensions.configuration.usersecrets/9.0.9", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-zQV2WOSP+3z1EuK91ULxfGgo2Y75bTRnmJHp08+w/YXAyekZutX/qCd88/HOMNh35MDW9mJJJxPpMPS+1Rww8A==", "path": "microsoft.extensions.dependencyinjection/9.0.9", "hashPath": "microsoft.extensions.dependencyinjection.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-/hymojfWbE9AlDOa0mczR44m00Jj+T3+HZO0ZnVTI032fVycI0ZbNOVFP6kqZMcXiLSYXzR2ilcwaRi6dzeGyA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.9", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-fNGvKct2De8ghm0Bpfq0iWthtzIWabgOTi+gJhNOPhNJIowXNEUE2eZNW/zNCzrHVA3PXg2yZ+3cWZndC2IqYA==", "path": "microsoft.extensions.dependencymodel/9.0.9", "hashPath": "microsoft.extensions.dependencymodel.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-gtzl9SD6CvFYOb92qEF41Z9rICzYniM342TWbbJwN3eLS6a5fCLFvO1pQGtpMSnP3h1zHXupMEeKSA9musWYCQ==", "path": "microsoft.extensions.diagnostics/9.0.9", "hashPath": "microsoft.extensions.diagnostics.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-YHGmxccrVZ2Ar3eI+/NdbOHkd1/HzrHvmQ5yBsp0Gl7jTyBe6qcXNYjUt9v9JIO+Z14la44+YYEe63JSqs1fYg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.9", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-M1ZhL9QkBQ/k6l/Wjgcli5zrV86HzytQ+gQiNtk9vs9Ge1fb17KKZil9T6jd15p2x/BGfXpup7Hg55CC0kkfig==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.9", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-sRrPtEwbK23OCFOQ36Xn6ofiB0/nl54/BOdR7lJ/Vwg3XlyvUdmyXvFUS1EU5ltn+sQtbcPuy1l0hsysO8++SQ==", "path": "microsoft.extensions.fileproviders.physical/9.0.9", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-iQAgORaVIlkhcpxFnVEfjqNWfQCwBEEH7x2IanTwGafA6Tb4xiBoDWySTxUo3MV2NUV/PmwS/8OhT/elPnJCnw==", "path": "microsoft.extensions.filesystemglobbing/9.0.9", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-DmRsWH3g8yZGho/pLQ79hxhM2ctE1eDTZ/HbAnrD/uw8m+P2pRRJOoBVxlrhbhMP3/y3oAJoy0yITasfmilbTg==", "path": "microsoft.extensions.hosting/9.0.9", "hashPath": "microsoft.extensions.hosting.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-ORA4dICNz7cuwupPkjXpSuoiK6GMg0aygInBIQCCFEimwoHntRKdJqB59faxq2HHJuTPW3NsZm5EjN5P5Zh6nQ==", "path": "microsoft.extensions.hosting.abstractions/9.0.9", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-MaCB0Y9hNDs4YLu3HCJbo199WnJT8xSgajG1JYGANz9FkseQ5f3v/llu3HxLI6mjDlu7pa7ps9BLPWjKzsAAzQ==", "path": "microsoft.extensions.logging/9.0.9", "hashPath": "microsoft.extensions.logging.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-FEgpSF+Z9StMvrsSViaybOBwR0f0ZZxDm8xV5cSOFiXN/t+ys+rwAlTd/6yG7Ld1gfppgvLcMasZry3GsI9lGA==", "path": "microsoft.extensions.logging.abstractions/9.0.9", "hashPath": "microsoft.extensions.logging.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-Abuo+S0Sg+Ke6vzSh5Ell+lwJJM+CEIqg1ImtWnnqF6a/ibJkQnmFJi4/ekEw/0uAcdFKJXtGV7w6cFN0nyXeg==", "path": "microsoft.extensions.logging.configuration/9.0.9", "hashPath": "microsoft.extensions.logging.configuration.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-x3+W7IfW9Tg3sV+sU9N1039M4CqklaAecwhz9qNtjOCBdmg7h96JaL+NAvhYgZgweVJTJaxAvuO8I+ZZehE7Pg==", "path": "microsoft.extensions.logging.console/9.0.9", "hashPath": "microsoft.extensions.logging.console.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-q8IbjIzTjfaGfuf9LAuG3X9BytAWj2hWhLU61rEkit847oaSSbcdx/yybY3yL9RgVG1u9ctk7kbCv18M+7Fi6Q==", "path": "microsoft.extensions.logging.debug/9.0.9", "hashPath": "microsoft.extensions.logging.debug.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-1SX5+mv16SBb5NrtLNxIvUt8PHbdvDloZazQdxz1CNM39jG7yeF6olH3sceQ4ONF0oVD5mVUsTag0iVX4xgyog==", "path": "microsoft.extensions.logging.eventlog/9.0.9", "hashPath": "microsoft.extensions.logging.eventlog.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-rGQi5mImot7tTFxj1tQWknWjOBHX1+gsX1WLmQNl5WHr4Sx1kXUBGDuRUjfx4c8pe/hcYHdalAmgk7RdusW6Jw==", "path": "microsoft.extensions.logging.eventsource/9.0.9", "hashPath": "microsoft.extensions.logging.eventsource.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-loxGGHE1FC2AefwPHzrjPq7X92LQm64qnU/whKfo6oWaceewPUVYQJBJs3S3E2qlWwnCpeZ+dGCPTX+5dgVAuQ==", "path": "microsoft.extensions.options/9.0.9", "hashPath": "microsoft.extensions.options.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-n4DCdnn2qs6V5U06Sx62FySEAZsJiJJgOzrPHDh9hPK7c2W8hEabC76F3Re3tGPjpiKa02RvB6FxZyxo8iICzg==", "path": "microsoft.extensions.options.configurationextensions/9.0.9", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-z4pyMePOrl733ltTowbN565PxBw1oAr8IHmIXNDiDqd22nFpYltX9KhrNC/qBWAG1/Zx5MHX+cOYhWJQYCO/iw==", "path": "microsoft.extensions.primitives/9.0.9", "hashPath": "microsoft.extensions.primitives.9.0.9.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.3485.44": {"type": "package", "serviceable": true, "sha512": "sha512-4hk+MDJcW6Hcvtfb0qv0zba9mpBFxM2GO9gJ+8Nq/zoAdwFanmt7jzvM07NPB/UcuQ/jZpKh9IUDL/eVOEFh2A==", "path": "microsoft.web.webview2/1.0.3485.44", "hashPath": "microsoft.web.webview2.1.0.3485.44.nupkg.sha512"}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"type": "package", "serviceable": true, "sha512": "sha512-o0T4CVaumDjPNNijKiM7p25vHKdyKqYvaVVLgQO02KTOoUDlgMYJVUQAXn1IG0G9/ZsdZ+bdgWxgQsrO/b37qw==", "path": "microsoft.windows.sdk.buildtools/10.0.26100.4948", "hashPath": "microsoft.windows.sdk.buildtools.10.0.26100.4948.nupkg.sha512"}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"type": "package", "serviceable": true, "sha512": "sha512-IMdvRmCIZnBS5GkYnv0po1bcx6U1OF39pqA4TphQ9evDzpCRoSE19/PkDvlUNNrBavTsLIEJgd/TAIFner75ow==", "path": "microsoft.windows.sdk.buildtools.msix/1.7.20250829.1", "hashPath": "microsoft.windows.sdk.buildtools.msix.1.7.20250829.1.nupkg.sha512"}, "Microsoft.WindowsAppSDK/1.8.250907003": {"type": "package", "serviceable": true, "sha512": "sha512-FCTiOXXnp9EGvVAuLtQc9LT41kj4JZ1Nis9pTrNCubjOrIQAzpJdA3OfWuFCMktsx/s/nWbpQ1JQ4jUAQQDoLA==", "path": "microsoft.windowsappsdk/1.8.250907003", "hashPath": "microsoft.windowsappsdk.1.8.250907003.nupkg.sha512"}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"type": "package", "serviceable": true, "sha512": "sha512-WvH7ur+R2N8c3deB8y7q7+Wwx7zybkC6LMS/KNqSYXlSOr75/WCZYwqwrPHJ/63YIUVhka7nJos9g4rIe7SFCw==", "path": "microsoft.windowsappsdk.ai/1.8.37", "hashPath": "microsoft.windowsappsdk.ai.1.8.37.nupkg.sha512"}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"type": "package", "serviceable": true, "sha512": "sha512-8LlfXBS2Hpw+OoVXViJmIOPXl0nMbqMaFR3j6+QHFNc62VULwPEcXiMRcP2WbV/+mtC7W2LH6yx6uu/Hrr9lVw==", "path": "microsoft.windowsappsdk.base/1.8.250831001", "hashPath": "microsoft.windowsappsdk.base.1.8.250831001.nupkg.sha512"}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"type": "package", "serviceable": true, "sha512": "sha512-WJ0p9yMgiNYqU2O5ZKCXcb7FBjryIUUopgeYMvnlf1yBUYgdjMFMkoJqYVqkz866wnntiB2IZhLxEzhFzvVs1A==", "path": "microsoft.windowsappsdk.dwrite/1.8.25090401", "hashPath": "microsoft.windowsappsdk.dwrite.1.8.25090401.nupkg.sha512"}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"type": "package", "serviceable": true, "sha512": "sha512-ltIXeHUX0AATpqmx/oBcRK+zhtK0KAfoGqItlQRlef9kG7Itj9iXAI+1EdFr4cQYzHzFM3PPLszEWDyR633svA==", "path": "microsoft.windowsappsdk.foundation/1.8.250906002", "hashPath": "microsoft.windowsappsdk.foundation.1.8.250906002.nupkg.sha512"}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"type": "package", "serviceable": true, "sha512": "sha512-UoK2yeZiycD1DmADHZz+hcMAoOaUfXLc9qUPfOjmVeKQ6i5ghGMjx/nd49bksP3wVhmSGHxb3argRKWPkK5maw==", "path": "microsoft.windowsappsdk.interactiveexperiences/1.8.250906004", "hashPath": "microsoft.windowsappsdk.interactiveexperiences.1.8.250906004.nupkg.sha512"}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"type": "package", "serviceable": true, "sha512": "sha512-URsthdat9pv1wnRNAy0WA5yejsc47QsSjjJ+L6INEgIFilrp4/LYndpHkoWh3KwBSjwkskvZlSprbOl09YVg/g==", "path": "microsoft.windowsappsdk.runtime/1.8.250907003", "hashPath": "microsoft.windowsappsdk.runtime.1.8.250907003.nupkg.sha512"}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"type": "package", "serviceable": true, "sha512": "sha512-sgwdXYhb8S4JjBmWWiFxALT1xK0fJeAbisolctmodMX7tlvBXDgUyvl/GHfTQ61DGIiW+kokX61WR46L2YlhAA==", "path": "microsoft.windowsappsdk.widgets/1.8.250904007", "hashPath": "microsoft.windowsappsdk.widgets.1.8.250904007.nupkg.sha512"}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"type": "package", "serviceable": true, "sha512": "sha512-6oskwUluqlDGwUcwYlY3GWTMLajyjh9e790SmWzCCMDRV6sunYbqp7DkiSLzn8nhgSbGvmj6zG92JnkYRlbrXw==", "path": "microsoft.windowsappsdk.winui/1.8.250906003", "hashPath": "microsoft.windowsappsdk.winui.1.8.250906003.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-wpsUfnyv8E5K4WQaok6weewvAbQhcLwXFcHBm5U0gdEaBs85N//ssuYvRPFWwz2rO/9/DFP3A1sGMzUFBj8y3w==", "path": "system.diagnostics.eventlog/9.0.9", "hashPath": "system.diagnostics.eventlog.9.0.9.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Json/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-NEnpppwq67fRz/OvQRxsEMgetDJsxlxpEsAFO/4PZYbAyAMd4Ol6KS7phc8uDoKPsnbdzRLKobpX303uQwCqdg==", "path": "system.text.json/9.0.9", "hashPath": "system.text.json.9.0.9.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "Microsoft.Web.WebView2.Core.Projection/1.0.3485.44": {"type": "reference", "serviceable": false, "sha512": ""}}}